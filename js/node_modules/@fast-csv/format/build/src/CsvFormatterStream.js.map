{"version": 3, "file": "CsvFormatterStream.js", "sourceRoot": "", "sources": ["../../src/CsvFormatterStream.ts"], "names": [], "mappings": ";;;AAAA,mCAAsD;AAGtD,2CAA2C;AAE3C,MAAa,kBAAiD,SAAQ,kBAAS;IAO3E,YAAmB,gBAAwC;QACvD,KAAK,CAAC,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC;QAHvD,kBAAa,GAAG,KAAK,CAAC;QAI1B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,wBAAY,CAAC,gBAAgB,CAAC,CAAC;QACvD,wCAAwC;QACxC,wEAAwE;QACxE,IAAI,CAAC,aAAa,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACpD,CAAC;IAEM,SAAS,CAAC,iBAA6C;QAC1D,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,iBAAiB,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,UAAU,CAAC,GAAM,EAAE,QAAgB,EAAE,EAAqB;QAC7D,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC7B;YACD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,EAAQ,EAAE;gBAC9C,IAAI,GAAG,EAAE;oBACL,QAAQ,GAAG,IAAI,CAAC;oBAChB,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;iBAClB;gBACD,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAQ,EAAE;wBACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBACtC,CAAC,CAAC,CAAC;iBACN;gBACD,QAAQ,GAAG,IAAI,CAAC;gBAChB,OAAO,EAAE,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,QAAQ,EAAE;gBACV,MAAM,CAAC,CAAC;aACX;YACD,EAAE,CAAC,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEM,MAAM,CAAC,EAAqB;QAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAQ,EAAE;YACzC,IAAI,GAAG,EAAE;gBACL,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;aAClB;YACD,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAQ,EAAE;oBACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;aACN;YACD,OAAO,EAAE,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AA9DD,gDA8DC"}