{"name": "archiver-utils", "version": "2.1.0", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "devDependencies": {"chai": "^4.2.0", "mkdirp": "^0.5.0", "mocha": "^5.0.0", "rimraf": "^2.6.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}}