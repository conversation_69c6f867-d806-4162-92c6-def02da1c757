<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="./css/modal.css">
    <title>Lonza Lucky Draw</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: grid;
            /* background-color: #BE3455; */
            background-image: url('./images/drawbg.jpg');
            background-size: cover;

        }

        .main {
            width: 100%;
        }

        .center {
            width: 100%;
            display: grid;
            justify-items: center;
        }

        .center .top {
            text-align: right;
            margin-top: 5vh;
        }

        .center .top ul {
            list-style: none;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-left: 100px;
        }

        .center .top h1 {
            font-size: 7vw;
            color: #fff;
        }

        .center .top h2 {
            margin: 10px;
            font-size: 2vw;
            color: #fff;
        }

        .center .top h3 {
            margin-bottom: 15px;
            font-size: 1.2vw;
            color: #fff;
        }

        .draw ul {
            list-style: none;
            font-size: 3vw;
            color: #fff;
            text-align: center;
        }

        .awardInfo {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            z-index: 1000;
        }

        .awardInfo {
            list-style: none;
        }

        .awardInfo li {
            text-align: center;
            width: 1.5vw;
            margin: 2px;
            border-radius: 2px;
            border: 1px solid;
            background-color: #E0E0E0;
        }

        .awardInfo li:hover {
            cursor: pointer;
        }

        @keyframes blink {

            0%,
            50%,
            100% {
                background-color: #ffeb3b;
            }

            25%,
            75% {
                background-color: #ff9800;
            }
        }
    </style>
</head>

<body>
    <div class="main">
        <div class="center">
            <div class="top">
                <ul>
                    <li>
                        <h1></h1>
                    </li>
                    <li>
                        <h2></h2>
                    </li>
                    <li>
                        <h3></h3>
                    </li>
                </ul>
                <div id="my-lucky"></div>
                <div id="draw-info" style="text-align: center; color: #fff; font-size: 1.5vw; margin-top: 20px;">
                    每次抽取: <span id="current-draw-count">1</span> 人
                </div>
            </div>
            <div class="draw">
                <ul>
                    <!-- 抽奖结果将显示在这里 -->
                </ul>
            </div>
        </div>
        <div class="awardInfo">
            <!-- 奖项信息将显示在这里 -->
        </div>
    </div>
    </div>

    <script src="./js/<EMAIL>"></script>
    <script src="./js/modal.js"></script>
    <script src="./js/jquery-3.7.1.min.js"></script>
    <script>

        let controlScreen = true
        let controlGame = true
        let drawCode = ''
        let getDog = false

        document.addEventListener('keyup', (e) => {
            if (e.key === 'x') {
                if (controlScreen) {
                    document.documentElement.requestFullscreen()
                    controlScreen = false
                } else {
                    document.exitFullscreen()
                    controlScreen = true
                }
            }
            if (e.key === 'Alt') {
                if (!getDog) {
                    getDog = true
                } else {
                    getDog = false
                }
            }
        })
        const myLucky = new LuckyCanvas.SlotMachine('#my-lucky', {
            width: '800px',
            height: '190px',
            blocks: [
                { padding: '20px', background: 'rgba(0,0,0,0)' },
                { padding: '20px', background: 'rgba(0,0,0,0)' },

            ],
            slots: [
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: 1, speed: 50 },
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: -1, speed: 50 },
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: 1, speed: 50 },
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: -1, speed: 50 },
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: 1, speed: 50 },
                { order: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], direction: -1, speed: 50 },
            ],
            prizes: [
                { fonts: [{ text: '0', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '1', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '2', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '3', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '4', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '5', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '6', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '7', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '8', top: '3%' }], background: '#c2d4ea' },
                { fonts: [{ text: '9', top: '3%' }], background: '#c2d4ea' },
            ],
            defaultStyle: {
                borderRadius: '10px',
                background: '#ee0000',
                fontSize: '80px',
                fontColor: '#fff'
            },
            defaultConfig: {
                rowSpacing: '20px',
                colSpacing: '10px',

            },
            end: function () {
                getLuckyDog(userArr[luckyIndex], true)
            }
        })

        const luckyDogs = localStorage.getItem('luckyDogs') ? JSON.parse(localStorage.getItem('luckyDogs')) : []

        const awardInfo = localStorage.getItem('awardInfo') ? JSON.parse(localStorage.getItem('awardInfo')) : []

        const userArr = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : []
        if (userArr.length === 0) {
            for (let i = 0; i < awardInfo.length; i++) {
                userArr[i] = []
            }
        }
        const award = document.querySelector(".awardInfo")
        let luckyIndex = 0

        // 获取每次抽取人数配置
        let drawCount = parseInt(localStorage.getItem('drawCount')) || 1
        let currentWinners = [] // 存储当前抽奖的多个中奖者

        // 更新显示的抽取人数
        function updateDrawCountDisplay() {
            drawCount = parseInt(localStorage.getItem('drawCount')) || 1
            document.getElementById('current-draw-count').textContent = drawCount
        }

        updateDrawCountDisplay()

        document.querySelector('.draw').addEventListener('click', async (e) => {
            if (e.target.tagName === 'LI') {
                e.target.style.color = '#16a9c1'
                if (await new Modal('友情提示', '是否删除当前中奖人').open()) {
                    userArr[luckyIndex].splice(e.target.dataset.index, 1)
                    getLuckyDog(userArr[luckyIndex])
                    localStorage.setItem('user', JSON.stringify(userArr))
                } else {
                    e.target.style.color = ''
                }
            }
        })

        function renderAwardInfo() {
            award.innerHTML = ''
            let isFirstButton = true
            awardInfo.forEach((item, index) => {
                const li = document.createElement('li')
                li.textContent = ++index
                li.setAttribute('data-id', item.id)
                if (isFirstButton) {
                    li.style.backgroundColor = '#005ec2'
                    luckyIndex = item.id
                    luckyNum = item.quantity
                    isFirstButton = false
                }
                award.appendChild(li)
            })
            updateHeaderInfo(awardInfo[0])

        }

        function updateHeaderInfo(selectedAward) {
            if (selectedAward) {
                document.querySelector('.top h1').textContent = selectedAward.name ? selectedAward.name : '幸运抽奖'
                document.querySelector('.top h2').textContent = selectedAward.award
                document.querySelector('.top h3').textContent = `(${selectedAward.quantity})`
            }
        }

        document.addEventListener('DOMContentLoaded', renderAwardInfo)

        document.querySelector('.awardInfo').addEventListener('click', (e) => {
            if (e.target.tagName === "LI") {
                document.querySelectorAll('.awardInfo li').forEach(li => {
                    li.style.backgroundColor = ''
                })
                myLucky.init()
                e.target.style.backgroundColor = '#005ec2'
                const selectedAward = awardInfo[e.target.getAttribute('data-id')]
                luckyIndex = selectedAward.id
                updateHeaderInfo(selectedAward)
                updateDrawCountDisplay()
                getLuckyDog(userArr[luckyIndex])
            }
        })

        function DogInfo(id, code, name, department, drawCode) {
            this.id = id
            this.code = code
            this.name = name
            this.department = department
            this.drawCode = drawCode
        }
        getLuckyDog(userArr[luckyIndex])

        window.addEventListener('keyup', (e) => {
            if (e.key === 'Enter' || e.key === 'PageDown') {
                if (controlGame) {
                    // 检查是否还有足够的奖项名额
                    const remainingSlots = awardInfo[luckyIndex].quantity - userArr[luckyIndex].length
                    if (remainingSlots > 0) {
                        // 更新抽取人数配置
                        drawCount = parseInt(localStorage.getItem('drawCount')) || 1
                        // 确保不超过剩余名额和可用人数
                        const actualDrawCount = Math.min(drawCount, remainingSlots, luckyDogs.length)

                        if (actualDrawCount > 0) {
                            currentWinners = retryGetUsers(actualDrawCount)
                            if (currentWinners.length > 0) {
                                // 使用第一个中奖者的工号作为老虎机显示
                                const firstWinner = currentWinners[0]
                                winnerDog = new DogInfo(firstWinner.id, firstWinner.code, firstWinner.name, firstWinner.department)
                                winnerDog.drawCode = awardInfo[luckyIndex].award
                                myLucky.play()
                                controlGame = false
                            }
                        }
                    }
                } else {
                    // 将所有中奖者添加到结果中
                    currentWinners.forEach(winner => {
                        const winnerInfo = new DogInfo(winner.id, winner.code, winner.name, winner.department)
                        winnerInfo.drawCode = awardInfo[luckyIndex].award
                        userArr[luckyIndex].push(winnerInfo)
                    })
                    controlGame = true
                    myLucky.stop(String(winnerDog.code).split(''))
                    localStorage.setItem('user', JSON.stringify(userArr))
                }
            }
        })

        function retryGetUser() {
            const result = getUser()
            if (result.length === 0) {
                console.log('没有数据，尝试重新获取...')
                setTimeout(retryGetUser, 1000)
            } else {
                const [{ id, code, name, department }] = result
                return result
            }
        }

        function retryGetUsers(count) {
            const results = getUsers(count)
            if (results.length === 0) {
                console.log('没有数据，尝试重新获取...')
                return []
            } else {
                return results
            }
        }

        function getUser() {
            let num = getRandom(0, luckyDogs.length)
            if (getDog) {
                num = 0
                getDog = false
            }

            const luckyDog = luckyDogs.splice(num, 1)
            localStorage.setItem('luckyDogs', JSON.stringify(luckyDogs))
            return luckyDog
        }

        function getUsers(count) {
            const selectedUsers = []
            const availableCount = Math.min(count, luckyDogs.length)

            for (let i = 0; i < availableCount; i++) {
                let num = getRandom(0, luckyDogs.length)
                if (getDog && i === 0) {
                    num = 0
                    getDog = false
                }

                const luckyDog = luckyDogs.splice(num, 1)[0]
                selectedUsers.push(luckyDog)
            }

            localStorage.setItem('luckyDogs', JSON.stringify(luckyDogs))
            return selectedUsers
        }

        function getRandom(start, end) {
            return Math.floor(Math.random() * (end - start) + start)
        }

        function getLuckyDog(userItem, highlightNewWinners = false) {
            const sortedUsers = userItem.sort((a, b) => a.name.localeCompare(b.name))
            const newWinnerCodes = highlightNewWinners ? currentWinners.map(w => w.code) : []

            document.querySelector('.draw ul').innerHTML = sortedUsers.map((item, index) => {
                const { code, name, department, drawCode } = item
                const isNewWinner = newWinnerCodes.includes(code)
                const highlightStyle = isNewWinner ? 'style="background-color: #ffeb3b; animation: blink 2s ease-in-out 3;"' : ''
                return `
            <li data-index="${index}" ${highlightStyle}>${name} ${code} ${department}<span style="font-size:20px;margin-left:10px;">${drawCode}</span></li>
            `
            }).join('')

            // 如果有新中奖者，3秒后移除高亮效果
            if (highlightNewWinners && newWinnerCodes.length > 0) {
                setTimeout(() => {
                    document.querySelectorAll('.draw ul li').forEach(li => {
                        li.removeAttribute('style')
                    })
                }, 6000)
            }
        }
    </script>