.modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    background-color: #fff;
    z-index: 1000;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 5px 5px 5px 5px rgba(0, 0, 0, 0.1);
}

.modal .title {
    font-size: 30px;
    color: #333;
    margin-bottom: 15px;
    text-align: left;
}

.modal .mess {
    font-size: 20px;
    color: #666;
    margin-bottom: 20px;
    text-align: center;
}

.modal button {
    width: 80px;
    padding: 10px;
    margin-left: 20px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    border-radius: 3px;
    color: #fff;
    background-color: #007bff;
    outline: none;
    margin-top: 10px;
    transition: background-color 0.3s;
}

.modal .modalButtons {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.modal button:hover {
    background-color: #0056b3;
}

.modal .cancel {
    background-color: #6c757d;
}

.modal .cancel:hover {
    background-color: #5a6268;
}